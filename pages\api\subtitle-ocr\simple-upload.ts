import { NextApiRequest, NextApiResponse } from 'next'
import { IncomingForm } from 'formidable'
import fs from 'fs'
import path from 'path'

export const config = {
  api: {
    bodyParser: false,
  },
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      message: 'Method not allowed' 
    })
  }

  try {
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'data', 'videos')
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }

    // 使用Promise包装formidable
    const parseForm = (): Promise<{ fields: any; files: any }> => {
      return new Promise((resolve, reject) => {
        const form = new IncomingForm({
          uploadDir,
          keepExtensions: true,
          maxFileSize: 500 * 1024 * 1024, // 500MB
        })

        form.parse(req, (err, fields, files) => {
          if (err) {
            reject(err)
          } else {
            resolve({ fields, files })
          }
        })
      })
    }

    const { files } = await parseForm()
    const file = Array.isArray(files.video) ? files.video[0] : files.video

    if (!file) {
      return res.status(400).json({
        success: false,
        message: '没有上传视频文件'
      })
    }

    // 验证文件格式
    const allowedExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    const fileExtension = path.extname(file.originalFilename || '').toLowerCase()
    
    if (!allowedExtensions.includes(fileExtension)) {
      // 删除不支持的文件
      try {
        if (fs.existsSync(file.filepath)) {
          fs.unlinkSync(file.filepath)
        }
      } catch (deleteError) {
        console.warn('删除无效文件失败:', deleteError)
      }
      
      return res.status(400).json({
        success: false,
        message: `不支持的视频格式: ${fileExtension}。支持的格式: ${allowedExtensions.join(', ')}`
      })
    }

    // 重命名文件
    const timestamp = Date.now()
    const newFilename = `${timestamp}_${file.originalFilename}`
    const newPath = path.join(uploadDir, newFilename)
    
    fs.renameSync(file.filepath, newPath)

    // 返回基本信息（不进行ffmpeg处理以避免复杂性）
    const stats = fs.statSync(newPath)
    
    const result = {
      filename: newFilename,
      size: stats.size,
      duration: 0, // 暂时设为0，后续可以通过其他API获取
      width: 0,
      height: 0,
      fps: 0,
      format: 'unknown',
      uploadPath: newPath
    }

    res.status(200).json({
      success: true,
      data: result,
      message: '视频上传成功'
    })

  } catch (error) {
    console.error('视频上传失败:', error)
    res.status(500).json({
      success: false,
      message: '视频上传失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}
