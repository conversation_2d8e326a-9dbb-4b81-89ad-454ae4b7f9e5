'use client'

import React from 'react'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Film, 
  Search, 
  Eye, 
  FileText, 
  CheckCircle, 
  Loader2,
  Clock
} from 'lucide-react'

interface ProcessingState {
  stage: 'idle' | 'uploading' | 'extracting' | 'detecting' | 'recognizing' | 'generating' | 'completed'
  progress: number
  message: string
  error?: string
}

interface ProcessingProgressProps {
  state: ProcessingState
}

const stageConfig = {
  idle: {
    icon: Clock,
    label: '待处理',
    color: 'bg-gray-500',
    description: '等待开始处理'
  },
  uploading: {
    icon: Upload,
    label: '上传视频',
    color: 'bg-blue-500',
    description: '正在上传视频文件到服务器'
  },
  extracting: {
    icon: Film,
    label: '提取帧',
    color: 'bg-purple-500',
    description: '正在从视频中提取关键帧'
  },
  detecting: {
    icon: Search,
    label: '检测区域',
    color: 'bg-orange-500',
    description: '正在检测字幕区域位置'
  },
  recognizing: {
    icon: Eye,
    label: 'OCR识别',
    color: 'bg-indigo-500',
    description: '正在进行光学字符识别'
  },
  generating: {
    icon: FileText,
    label: '生成字幕',
    color: 'bg-green-500',
    description: '正在生成字幕文件'
  },
  completed: {
    icon: CheckCircle,
    label: '处理完成',
    color: 'bg-emerald-500',
    description: '字幕提取已完成'
  }
}

export function ProcessingProgress({ state }: ProcessingProgressProps) {
  const currentStage = stageConfig[state.stage]
  const IconComponent = currentStage.icon

  // 获取所有阶段的进度状态
  const getStageStatus = (stage: keyof typeof stageConfig) => {
    const stages = ['uploading', 'extracting', 'detecting', 'recognizing', 'generating', 'completed']
    const currentIndex = stages.indexOf(state.stage)
    const stageIndex = stages.indexOf(stage)
    
    if (stageIndex < currentIndex) return 'completed'
    if (stageIndex === currentIndex) return 'active'
    return 'pending'
  }

  return (
    <Card>
      <CardContent className="p-6">
        {/* 当前阶段信息 */}
        <div className="flex items-center gap-4 mb-6">
          <div className={`p-3 rounded-full ${currentStage.color}`}>
            {state.stage === 'completed' ? (
              <IconComponent className="h-6 w-6 text-white" />
            ) : (
              <Loader2 className="h-6 w-6 text-white animate-spin" />
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-lg">{currentStage.label}</h3>
              <Badge variant={state.stage === 'completed' ? 'default' : 'secondary'}>
                {state.progress}%
              </Badge>
            </div>
            <p className="text-gray-600 text-sm">{state.message || currentStage.description}</p>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mb-6">
          <Progress 
            value={state.progress} 
            className="h-2"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0%</span>
            <span>{state.progress}%</span>
            <span>100%</span>
          </div>
        </div>

        {/* 阶段步骤 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm text-gray-700 mb-3">处理步骤</h4>
          
          {Object.entries(stageConfig).slice(1, -1).map(([stage, config]) => {
            const status = getStageStatus(stage as keyof typeof stageConfig)
            const StageIcon = config.icon
            
            return (
              <div key={stage} className="flex items-center gap-3">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center
                  ${status === 'completed' ? 'bg-green-100 text-green-600' : 
                    status === 'active' ? `${config.color} text-white` : 
                    'bg-gray-100 text-gray-400'}
                `}>
                  {status === 'completed' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : status === 'active' ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <StageIcon className="h-4 w-4" />
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${
                      status === 'completed' ? 'text-green-600' :
                      status === 'active' ? 'text-gray-900' :
                      'text-gray-500'
                    }`}>
                      {config.label}
                    </span>
                    {status === 'completed' && (
                      <Badge variant="outline" className="text-xs">
                        已完成
                      </Badge>
                    )}
                    {status === 'active' && (
                      <Badge variant="default" className="text-xs">
                        进行中
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {config.description}
                  </p>
                </div>
              </div>
            )
          })}
        </div>

        {/* 预计时间 */}
        {state.stage !== 'completed' && state.stage !== 'idle' && (
          <div className="mt-6 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">预计处理时间</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              根据视频长度和设置，整个过程可能需要 2-10 分钟
            </p>
          </div>
        )}

        {/* 完成信息 */}
        {state.stage === 'completed' && (
          <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-700">处理完成！</span>
            </div>
            <p className="text-sm text-green-600">
              字幕提取已完成，您可以在右侧"提取结果"标签页中查看和编辑字幕内容。
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
