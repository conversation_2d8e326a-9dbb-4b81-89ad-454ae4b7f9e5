'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Settings, Languages, Target, Zap, Clock } from 'lucide-react'

interface OCRConfig {
  language: string
  detectThreshold: number
  recognizeThreshold: number
  frameRate: number
  subtitleRegion: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

interface OCRSettingsProps {
  config: OCRConfig
  onChange: (config: OCRConfig) => void
  disabled?: boolean
}

export function OCRSettings({ config, onChange, disabled = false }: OCRSettingsProps) {
  
  const updateConfig = (updates: Partial<OCRConfig>) => {
    onChange({ ...config, ...updates })
  }

  const updateRegion = (updates: Partial<OCRConfig['subtitleRegion']>) => {
    onChange({
      ...config,
      subtitleRegion: { ...config.subtitleRegion, ...updates }
    })
  }

  return (
    <div className="space-y-6">
      {/* OCR语言设置 */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Languages className="h-4 w-4" />
          <Label className="text-sm font-medium">OCR语言</Label>
        </div>
        <Select
          value={config.language}
          onValueChange={(value) => updateConfig({ language: value })}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="chinese">中文</SelectItem>
            <SelectItem value="english">英文</SelectItem>
            <SelectItem value="japanese">日文</SelectItem>
            <SelectItem value="korean">韩文</SelectItem>
            <SelectItem value="auto">自动检测</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      {/* 检测阈值设置 */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Target className="h-4 w-4" />
          <Label className="text-sm font-medium">文字检测阈值</Label>
        </div>
        <div className="space-y-2">
          <Slider
            value={[config.detectThreshold]}
            onValueChange={(value) => updateConfig({ detectThreshold: value[0] })}
            min={0.1}
            max={0.9}
            step={0.1}
            disabled={disabled}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>低敏感度 (0.1)</span>
            <span className="font-medium">{config.detectThreshold}</span>
            <span>高敏感度 (0.9)</span>
          </div>
        </div>
        <p className="text-xs text-gray-500">
          较低的值会检测更多文字区域，但可能包含噪声
        </p>
      </div>

      {/* 识别阈值设置 */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Zap className="h-4 w-4" />
          <Label className="text-sm font-medium">文字识别阈值</Label>
        </div>
        <div className="space-y-2">
          <Slider
            value={[config.recognizeThreshold]}
            onValueChange={(value) => updateConfig({ recognizeThreshold: value[0] })}
            min={0.1}
            max={0.9}
            step={0.1}
            disabled={disabled}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>低要求 (0.1)</span>
            <span className="font-medium">{config.recognizeThreshold}</span>
            <span>高要求 (0.9)</span>
          </div>
        </div>
        <p className="text-xs text-gray-500">
          较高的值只保留高置信度的识别结果
        </p>
      </div>

      <Separator />

      {/* 帧率设置 */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4" />
          <Label className="text-sm font-medium">提取帧率 (帧/秒)</Label>
        </div>
        <div className="space-y-2">
          <Slider
            value={[config.frameRate]}
            onValueChange={(value) => updateConfig({ frameRate: value[0] })}
            min={0.5}
            max={5}
            step={0.5}
            disabled={disabled}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>0.5 fps</span>
            <span className="font-medium">{config.frameRate} fps</span>
            <span>5 fps</span>
          </div>
        </div>
        <p className="text-xs text-gray-500">
          较高的帧率提取更多帧，但处理时间更长
        </p>
      </div>

      <Separator />

      {/* 字幕区域设置 */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          <Label className="text-sm font-medium">字幕区域设置 (%)</Label>
        </div>
        
        {/* 上边距 */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">上边距</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[config.subtitleRegion.top]}
              onValueChange={(value) => updateRegion({ top: value[0] })}
              min={0}
              max={100}
              step={1}
              disabled={disabled}
              className="flex-1"
            />
            <Input
              type="number"
              value={config.subtitleRegion.top}
              onChange={(e) => updateRegion({ top: parseInt(e.target.value) || 0 })}
              min={0}
              max={100}
              disabled={disabled}
              className="w-16 h-8 text-xs"
            />
          </div>
        </div>

        {/* 下边距 */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">下边距</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[config.subtitleRegion.bottom]}
              onValueChange={(value) => updateRegion({ bottom: value[0] })}
              min={0}
              max={100}
              step={1}
              disabled={disabled}
              className="flex-1"
            />
            <Input
              type="number"
              value={config.subtitleRegion.bottom}
              onChange={(e) => updateRegion({ bottom: parseInt(e.target.value) || 0 })}
              min={0}
              max={100}
              disabled={disabled}
              className="w-16 h-8 text-xs"
            />
          </div>
        </div>

        {/* 左边距 */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">左边距</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[config.subtitleRegion.left]}
              onValueChange={(value) => updateRegion({ left: value[0] })}
              min={0}
              max={100}
              step={1}
              disabled={disabled}
              className="flex-1"
            />
            <Input
              type="number"
              value={config.subtitleRegion.left}
              onChange={(e) => updateRegion({ left: parseInt(e.target.value) || 0 })}
              min={0}
              max={100}
              disabled={disabled}
              className="w-16 h-8 text-xs"
            />
          </div>
        </div>

        {/* 右边距 */}
        <div className="space-y-2">
          <Label className="text-xs text-gray-600">右边距</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[config.subtitleRegion.right]}
              onValueChange={(value) => updateRegion({ right: value[0] })}
              min={0}
              max={100}
              step={1}
              disabled={disabled}
              className="flex-1"
            />
            <Input
              type="number"
              value={config.subtitleRegion.right}
              onChange={(e) => updateRegion({ right: parseInt(e.target.value) || 0 })}
              min={0}
              max={100}
              disabled={disabled}
              className="w-16 h-8 text-xs"
            />
          </div>
        </div>

        <div className="p-3 bg-blue-50 rounded-lg">
          <p className="text-xs text-blue-700">
            💡 提示：字幕区域设置决定了OCR检测的范围。通常字幕出现在视频底部，建议设置上边距75%，下边距95%。
          </p>
        </div>
      </div>

      {/* 预设配置 */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">快速预设</Label>
        <div className="grid grid-cols-2 gap-2">
          <button
            className="p-2 text-xs border rounded hover:bg-gray-50 disabled:opacity-50"
            disabled={disabled}
            onClick={() => updateConfig({
              detectThreshold: 0.3,
              recognizeThreshold: 0.5,
              frameRate: 1,
              subtitleRegion: { top: 75, bottom: 95, left: 10, right: 90 }
            })}
          >
            标准设置
          </button>
          <button
            className="p-2 text-xs border rounded hover:bg-gray-50 disabled:opacity-50"
            disabled={disabled}
            onClick={() => updateConfig({
              detectThreshold: 0.2,
              recognizeThreshold: 0.3,
              frameRate: 2,
              subtitleRegion: { top: 70, bottom: 100, left: 5, right: 95 }
            })}
          >
            高精度设置
          </button>
        </div>
      </div>
    </div>
  )
}
