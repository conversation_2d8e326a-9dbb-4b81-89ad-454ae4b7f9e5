'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'

import { Separator } from '@/components/ui/separator'
import { 
  Download, 
  Edit, 
  Save, 
  X, 
  FileText, 
  Clock, 
  Star,
  Trash2,
  Plus,
  ArrowRight
} from 'lucide-react'

interface SubtitleEntry {
  index: number
  startTime: number
  endTime: number
  text: string
  confidence: number
}

interface SubtitlePreviewProps {
  entries: SubtitleEntry[]
  onExport: (format: 'srt' | 'vtt' | 'txt') => void
  onEntriesChange?: (entries: SubtitleEntry[]) => void
}

export function SubtitlePreview({ entries, onExport, onEntriesChange }: SubtitlePreviewProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [editingEntry, setEditingEntry] = useState<SubtitleEntry | null>(null)

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
    }
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
  }

  // 开始编辑
  const startEdit = (entry: SubtitleEntry) => {
    setEditingIndex(entry.index)
    setEditingEntry({ ...entry })
  }

  // 保存编辑
  const saveEdit = () => {
    if (editingEntry && onEntriesChange) {
      const newEntries = entries.map(entry => 
        entry.index === editingEntry.index ? editingEntry : entry
      )
      onEntriesChange(newEntries)
    }
    setEditingIndex(null)
    setEditingEntry(null)
  }

  // 取消编辑
  const cancelEdit = () => {
    setEditingIndex(null)
    setEditingEntry(null)
  }

  // 删除条目
  const deleteEntry = (index: number) => {
    if (onEntriesChange) {
      const newEntries = entries
        .filter(entry => entry.index !== index)
        .map((entry, i) => ({ ...entry, index: i + 1 }))
      onEntriesChange(newEntries)
    }
  }

  // 添加新条目
  const addEntry = () => {
    if (onEntriesChange) {
      const lastEntry = entries[entries.length - 1]
      const newEntry: SubtitleEntry = {
        index: entries.length + 1,
        startTime: lastEntry ? lastEntry.endTime + 1 : 0,
        endTime: lastEntry ? lastEntry.endTime + 3 : 2,
        text: '新字幕',
        confidence: 1.0
      }
      onEntriesChange([...entries, newEntry])
    }
  }

  // 跳转到AI生成页面
  const jumpToAIGeneration = () => {
    // 这里应该实现跳转到AI生成分集简介页面的逻辑
    console.log('跳转到AI生成页面')
  }

  if (entries.length === 0) {
    return (
      <div className="h-full flex flex-col items-center justify-center text-center p-6">
        <FileText className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">暂无字幕内容</h3>
        <p className="text-sm text-gray-500">
          请先上传视频并开始字幕提取
        </p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部信息 */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="font-medium">字幕条目</span>
            <Badge variant="secondary">{entries.length}</Badge>
          </div>
          <div className="flex items-center gap-1">
            <Button variant="outline" size="sm" onClick={addEntry}>
              <Plus className="h-3 w-3 mr-1" />
              添加
            </Button>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
          <div>
            <span>总时长: </span>
            <span className="font-medium">
              {entries.length > 0 ? formatTime(entries[entries.length - 1].endTime) : '0:00'}
            </span>
          </div>
          <div>
            <span>平均置信度: </span>
            <span className="font-medium">
              {entries.length > 0 ? 
                (entries.reduce((sum, e) => sum + e.confidence, 0) / entries.length * 100).toFixed(1) + '%' : 
                '0%'
              }
            </span>
          </div>
        </div>
      </div>

      {/* 字幕列表 */}
      <div className="flex-1 mb-4">
        <div className="space-y-2">
          {entries.map((entry) => (
            <Card key={entry.index} className="p-3">
              {editingIndex === entry.index && editingEntry ? (
                // 编辑模式
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">#{entry.index}</Badge>
                    <div className="flex items-center gap-2 text-sm">
                      <Input
                        type="number"
                        value={editingEntry.startTime}
                        onChange={(e) => setEditingEntry({
                          ...editingEntry,
                          startTime: parseFloat(e.target.value) || 0
                        })}
                        step={0.1}
                        className="w-20 h-7"
                      />
                      <span>-</span>
                      <Input
                        type="number"
                        value={editingEntry.endTime}
                        onChange={(e) => setEditingEntry({
                          ...editingEntry,
                          endTime: parseFloat(e.target.value) || 0
                        })}
                        step={0.1}
                        className="w-20 h-7"
                      />
                      <span>秒</span>
                    </div>
                  </div>
                  
                  <Textarea
                    value={editingEntry.text}
                    onChange={(e) => setEditingEntry({
                      ...editingEntry,
                      text: e.target.value
                    })}
                    className="min-h-[60px]"
                    placeholder="输入字幕内容..."
                  />
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <span className="text-xs text-gray-500">
                        置信度: {(editingEntry.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button variant="outline" size="sm" onClick={cancelEdit}>
                        <X className="h-3 w-3" />
                      </Button>
                      <Button size="sm" onClick={saveEdit}>
                        <Save className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                // 显示模式
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">#{entry.index}</Badge>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{formatTime(entry.startTime)} - {formatTime(entry.endTime)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-gray-500">
                          {(entry.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => startEdit(entry)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => deleteEntry(entry.index)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <p className="text-sm leading-relaxed bg-gray-50 p-2 rounded">
                    {entry.text}
                  </p>
                </div>
              )}
            </Card>
          ))}
        </div>
      </div>

      {/* 导出按钮 */}
      <div className="space-y-3">
        <Separator />
        
        <div className="space-y-2">
          <h4 className="text-sm font-medium">导出字幕</h4>
          <div className="grid grid-cols-3 gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onExport('srt')}
              className="text-xs"
            >
              <Download className="h-3 w-3 mr-1" />
              SRT
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onExport('vtt')}
              className="text-xs"
            >
              <Download className="h-3 w-3 mr-1" />
              VTT
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onExport('txt')}
              className="text-xs"
            >
              <Download className="h-3 w-3 mr-1" />
              TXT
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium">快速操作</h4>
          <Button 
            className="w-full" 
            size="sm"
            onClick={jumpToAIGeneration}
          >
            <ArrowRight className="h-3 w-3 mr-2" />
            跳转到AI生成分集简介
          </Button>
        </div>
      </div>
    </div>
  )
}
