import { NextApiRequest, NextApiResponse } from 'next'
import * as ort from 'onnxruntime-node'
import sharp from 'sharp'
import fs from 'fs'
import path from 'path'

// 模型路径
const MODELS_DIR = path.join(process.cwd(), 'public', 'models')
const DET_MODEL_PATH = path.join(MODELS_DIR, 'ch_PP-OCRv5_mobile_det.onnx')
const REC_MODEL_PATH = path.join(MODELS_DIR, 'ch_PP-OCRv5_rec_server_infer.onnx')
const CLS_MODEL_PATH = path.join(MODELS_DIR, 'ch_ppocr_mobile_v2.0_cls_infer.onnx')

// 全局模型实例
let detectionSession: ort.InferenceSession | null = null
let recognitionSession: ort.InferenceSession | null = null
let classificationSession: ort.InferenceSession | null = null

interface TextBox {
  box: number[][] // 四个角点坐标
  confidence: number
}

interface OCRResult {
  text: string
  confidence: number
  box: number[][]
}

interface OCRRequest {
  imagePath: string
  detectThreshold?: number // 检测阈值，默认0.3
  recognizeThreshold?: number // 识别阈值，默认0.5
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { 
      imagePath, 
      detectThreshold = 0.3, 
      recognizeThreshold = 0.5 
    }: OCRRequest = req.body

    if (!imagePath || !fs.existsSync(imagePath)) {
      return res.status(400).json({ message: '图片文件不存在' })
    }

    // 初始化模型（如果还未加载）
    await initializeModels()

    // 执行OCR
    const results = await performOCR(imagePath, detectThreshold, recognizeThreshold)

    res.status(200).json({
      success: true,
      data: {
        results,
        totalTexts: results.length
      },
      message: `成功识别 ${results.length} 个文本区域`
    })

  } catch (error) {
    console.error('OCR推理失败:', error)
    res.status(500).json({
      success: false,
      message: 'OCR推理失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 初始化模型
async function initializeModels() {
  try {
    if (!detectionSession) {
      console.log('加载文字检测模型...')
      detectionSession = await ort.InferenceSession.create(DET_MODEL_PATH)
    }

    if (!recognitionSession) {
      console.log('加载文字识别模型...')
      recognitionSession = await ort.InferenceSession.create(REC_MODEL_PATH)
    }

    if (!classificationSession) {
      console.log('加载方向分类模型...')
      classificationSession = await ort.InferenceSession.create(CLS_MODEL_PATH)
    }
  } catch (error) {
    console.error('模型加载失败:', error)
    throw new Error('模型加载失败')
  }
}

// 执行完整OCR流程
async function performOCR(imagePath: string, detectThreshold: number, recognizeThreshold: number): Promise<OCRResult[]> {
  // 1. 文字检测
  const textBoxes = await detectText(imagePath, detectThreshold)
  
  if (textBoxes.length === 0) {
    return []
  }

  // 2. 对每个检测到的文字区域进行识别
  const results: OCRResult[] = []
  
  for (const textBox of textBoxes) {
    try {
      // 裁剪文字区域
      const croppedImage = await cropTextRegion(imagePath, textBox.box)
      
      // 方向分类（如果需要）
      const rotatedImage = await classifyAndRotate(croppedImage)
      
      // 文字识别
      const recognition = await recognizeText(rotatedImage)
      
      if (recognition.confidence >= recognizeThreshold) {
        results.push({
          text: recognition.text,
          confidence: recognition.confidence,
          box: textBox.box
        })
      }
    } catch (error) {
      console.warn('单个文字区域处理失败:', error)
      continue
    }
  }

  return results
}

// 文字检测
async function detectText(imagePath: string, threshold: number): Promise<TextBox[]> {
  if (!detectionSession) throw new Error('检测模型未加载')

  // 预处理图片
  const imageBuffer = await sharp(imagePath)
    .resize(640, 640, { fit: 'inside', withoutEnlargement: true })
    .raw()
    .toBuffer({ resolveWithObject: true })

  const { data, info } = imageBuffer
  const { width, height, channels } = info

  // 转换为模型输入格式 (1, 3, H, W)
  const inputTensor = new Float32Array(1 * 3 * height * width)
  for (let i = 0; i < height * width; i++) {
    inputTensor[i] = data[i * channels] / 255.0 // R
    inputTensor[height * width + i] = data[i * channels + 1] / 255.0 // G
    inputTensor[height * width * 2 + i] = data[i * channels + 2] / 255.0 // B
  }

  // 推理
  const feeds = { x: new ort.Tensor('float32', inputTensor, [1, 3, height, width]) }
  const results = await detectionSession.run(feeds)

  // 后处理：解析检测结果
  return parseDetectionResults(results, threshold, width, height)
}

// 解析检测结果
function parseDetectionResults(results: any, threshold: number, width: number, height: number): TextBox[] {
  // 这里需要根据具体的PP-OCR检测模型输出格式来实现
  // 通常包括置信度图和几何信息
  const textBoxes: TextBox[] = []
  
  // 简化实现：假设模型输出包含边界框信息
  // 实际实现需要根据PP-OCR的具体输出格式来解析
  
  return textBoxes
}

// 裁剪文字区域
async function cropTextRegion(imagePath: string, box: number[][]): Promise<Buffer> {
  // 计算边界框
  const xs = box.map(point => point[0])
  const ys = box.map(point => point[1])
  const left = Math.min(...xs)
  const top = Math.min(...ys)
  const width = Math.max(...xs) - left
  const height = Math.max(...ys) - top

  return await sharp(imagePath)
    .extract({ left: Math.floor(left), top: Math.floor(top), width: Math.ceil(width), height: Math.ceil(height) })
    .toBuffer()
}

// 方向分类和旋转
async function classifyAndRotate(imageBuffer: Buffer): Promise<Buffer> {
  if (!classificationSession) return imageBuffer

  // 简化实现：直接返回原图
  // 实际实现需要进行方向分类和相应的旋转
  return imageBuffer
}

// 文字识别
async function recognizeText(imageBuffer: Buffer): Promise<{ text: string; confidence: number }> {
  if (!recognitionSession) throw new Error('识别模型未加载')

  // 简化实现：返回占位符结果
  // 实际实现需要进行文字识别推理
  return {
    text: '识别结果',
    confidence: 0.8
  }
}
