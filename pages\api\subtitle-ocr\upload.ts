import { NextApiRequest, NextApiResponse } from 'next'
import formidable from 'formidable'
import fs from 'fs'
import path from 'path'
import ffmpeg from 'fluent-ffmpeg'

export const config = {
  api: {
    bodyParser: false,
    responseLimit: false,
  },
}

interface VideoInfo {
  filename: string
  size: number
  duration: number
  width: number
  height: number
  fps: number
  format: string
  uploadPath: string
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('视频上传API被调用:', req.method)

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    // 确保上传目录存在
    const uploadDir = path.join(process.cwd(), 'data', 'videos')
    console.log('上传目录:', uploadDir)

    if (!fs.existsSync(uploadDir)) {
      console.log('创建上传目录')
      fs.mkdirSync(uploadDir, { recursive: true })
    }

    const form = formidable({
      uploadDir,
      keepExtensions: true,
      maxFileSize: 500 * 1024 * 1024, // 500MB限制
      multiples: false,
      filename: (_name, ext, part) => {
        return `${Date.now()}_${part.originalFilename || 'video'}${ext}`
      }
    })

    let files: any
    try {
      console.log('开始解析文件...')
      const [, parsedFiles] = await form.parse(req)
      files = parsedFiles
      console.log('文件解析成功:', Object.keys(files))
    } catch (parseError) {
      console.error('文件解析错误:', parseError)
      return res.status(400).json({
        success: false,
        message: '文件解析失败，请检查文件格式和大小',
        error: parseError instanceof Error ? parseError.message : '解析错误'
      })
    }

    const file = Array.isArray(files.video) ? files.video[0] : files.video

    if (!file) {
      return res.status(400).json({
        success: false,
        message: '没有上传视频文件'
      })
    }

    // 验证文件格式
    const allowedExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    const fileExtension = path.extname(file.originalFilename || '').toLowerCase()

    if (!allowedExtensions.includes(fileExtension)) {
      // 删除不支持的文件
      try {
        if (fs.existsSync(file.filepath)) {
          fs.unlinkSync(file.filepath)
        }
      } catch (deleteError) {
        console.warn('删除无效文件失败:', deleteError)
      }

      return res.status(400).json({
        success: false,
        message: `不支持的视频格式: ${fileExtension}。支持的格式: ${allowedExtensions.join(', ')}`
      })
    }

    // 获取视频信息
    const videoInfo = await getVideoInfo(file.filepath)
    
    // 生成新的文件名（避免重复）
    const timestamp = Date.now()
    const newFilename = `${timestamp}_${file.originalFilename}`
    const newPath = path.join(uploadDir, newFilename)
    
    // 重命名文件
    fs.renameSync(file.filepath, newPath)

    const result: VideoInfo = {
      filename: newFilename,
      size: file.size,
      duration: videoInfo.duration,
      width: videoInfo.width,
      height: videoInfo.height,
      fps: videoInfo.fps,
      format: videoInfo.format,
      uploadPath: newPath
    }

    res.status(200).json({
      success: true,
      data: result,
      message: '视频上传成功'
    })

  } catch (error) {
    console.error('视频上传失败:', error)

    // 清理可能存在的临时文件
    try {
      const uploadDir = path.join(process.cwd(), 'data', 'videos')
      if (fs.existsSync(uploadDir)) {
        const files = fs.readdirSync(uploadDir)
        files.forEach(file => {
          const filePath = path.join(uploadDir, file)
          const stat = fs.statSync(filePath)
          // 删除5分钟前的临时文件
          if (Date.now() - stat.mtime.getTime() > 5 * 60 * 1000) {
            fs.unlinkSync(filePath)
          }
        })
      }
    } catch (cleanupError) {
      console.warn('临时文件清理失败:', cleanupError)
    }

    res.status(500).json({
      success: false,
      message: '视频上传失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 获取视频信息
function getVideoInfo(filePath: string): Promise<{
  duration: number
  width: number
  height: number
  fps: number
  format: string
}> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(err)
        return
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video')
      if (!videoStream) {
        reject(new Error('未找到视频流'))
        return
      }

      resolve({
        duration: metadata.format.duration || 0,
        width: videoStream.width || 0,
        height: videoStream.height || 0,
        fps: eval(videoStream.r_frame_rate || '0') || 0,
        format: metadata.format.format_name || 'unknown'
      })
    })
  })
}
