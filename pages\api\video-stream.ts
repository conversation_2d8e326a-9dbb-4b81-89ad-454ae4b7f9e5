import { NextApiRequest, NextApiResponse } from 'next'
import fs from 'fs'
import path from 'path'

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { path: videoPath } = req.query

  if (!videoPath || typeof videoPath !== 'string') {
    return res.status(400).json({ message: '缺少视频路径参数' })
  }

  // 安全检查：确保路径在允许的目录内
  const allowedDir = path.join(process.cwd(), 'data', 'videos')
  const fullPath = path.resolve(videoPath)
  
  if (!fullPath.startsWith(allowedDir)) {
    return res.status(403).json({ message: '访问被拒绝' })
  }

  if (!fs.existsSync(fullPath)) {
    return res.status(404).json({ message: '视频文件不存在' })
  }

  try {
    const stat = fs.statSync(fullPath)
    const fileSize = stat.size
    const range = req.headers.range

    if (range) {
      // 支持范围请求（用于视频流）
      const parts = range.replace(/bytes=/, "").split("-")
      const start = parseInt(parts[0], 10)
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1
      const chunksize = (end - start) + 1
      const file = fs.createReadStream(fullPath, { start, end })
      
      res.writeHead(206, {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'video/mp4',
      })
      
      file.pipe(res)
    } else {
      // 完整文件响应
      res.writeHead(200, {
        'Content-Length': fileSize,
        'Content-Type': 'video/mp4',
      })
      
      fs.createReadStream(fullPath).pipe(res)
    }
  } catch (error) {
    console.error('视频流错误:', error)
    res.status(500).json({ message: '视频流错误' })
  }
}
