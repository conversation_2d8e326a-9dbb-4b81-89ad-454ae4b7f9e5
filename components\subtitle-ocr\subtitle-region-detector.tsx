'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Search, 
  Target, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react'

interface DetectedRegion {
  x: number
  y: number
  width: number
  height: number
  confidence: number
  frameIndex: number
  timestamp: number
}

interface SubtitleRegionDetectorProps {
  videoPath?: string
  framesDir?: string
  onRegionDetected?: (region: DetectedRegion) => void
  isDetecting?: boolean
  detectionProgress?: number
}

export function SubtitleRegionDetector({ 
  videoPath, 
  framesDir,
  onRegionDetected,
  isDetecting = false,
  detectionProgress = 0
}: SubtitleRegionDetectorProps) {
  const [detectedRegions, setDetectedRegions] = useState<DetectedRegion[]>([])
  const [selectedRegion, setSelectedRegion] = useState<DetectedRegion | null>(null)
  const [showAllRegions, setShowAllRegions] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 开始检测
  const startDetection = async () => {
    if (!framesDir || !videoPath) return

    try {
      const response = await fetch('/api/subtitle-ocr/detect-subtitle-region', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          framesDir,
          videoPath,
          startFromOneThird: true,
          maxDetectionFrames: 20,
          confidenceThreshold: 0.5
        })
      })

      const result = await response.json()
      
      if (result.success) {
        setDetectedRegions(result.data.detectedRegions || [])
        if (result.data.subtitleRegion) {
          setSelectedRegion(result.data.subtitleRegion)
          onRegionDetected?.(result.data.subtitleRegion)
        }
      }
    } catch (error) {
      console.error('字幕区域检测失败:', error)
    }
  }

  // 选择区域
  const selectRegion = (region: DetectedRegion) => {
    setSelectedRegion(region)
    onRegionDetected?.(region)
  }

  // 绘制检测结果
  const drawDetectionResults = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制所有检测到的区域
    if (showAllRegions) {
      detectedRegions.forEach((region, index) => {
        const isSelected = selectedRegion && 
          region.x === selectedRegion.x && 
          region.y === selectedRegion.y

        // 设置样式
        ctx.strokeStyle = isSelected ? '#10b981' : '#6b7280'
        ctx.lineWidth = isSelected ? 3 : 1
        ctx.setLineDash(isSelected ? [] : [5, 5])

        // 绘制矩形
        ctx.strokeRect(region.x, region.y, region.width, region.height)

        // 绘制标签
        ctx.fillStyle = isSelected ? '#10b981' : '#6b7280'
        ctx.font = '12px sans-serif'
        ctx.fillText(
          `区域${index + 1} (${(region.confidence * 100).toFixed(1)}%)`,
          region.x,
          region.y - 5
        )
      })
    } else if (selectedRegion) {
      // 只绘制选中的区域
      ctx.strokeStyle = '#10b981'
      ctx.lineWidth = 3
      ctx.setLineDash([])
      ctx.strokeRect(
        selectedRegion.x, 
        selectedRegion.y, 
        selectedRegion.width, 
        selectedRegion.height
      )

      ctx.fillStyle = '#10b981'
      ctx.font = '14px sans-serif'
      ctx.fillText(
        `字幕区域 (${(selectedRegion.confidence * 100).toFixed(1)}%)`,
        selectedRegion.x,
        selectedRegion.y - 8
      )
    }
  }

  useEffect(() => {
    drawDetectionResults()
  }, [detectedRegions, selectedRegion, showAllRegions])

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          字幕区域检测
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 检测控制 */}
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            onClick={startDetection}
            disabled={isDetecting || !framesDir}
          >
            {isDetecting ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            {isDetecting ? '检测中...' : '开始检测'}
          </Button>
          
          {detectedRegions.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAllRegions(!showAllRegions)}
            >
              {showAllRegions ? (
                <EyeOff className="h-4 w-4 mr-2" />
              ) : (
                <Eye className="h-4 w-4 mr-2" />
              )}
              {showAllRegions ? '隐藏全部' : '显示全部'}
            </Button>
          )}
        </div>

        {/* 检测进度 */}
        {isDetecting && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>检测进度</span>
              <span>{detectionProgress}%</span>
            </div>
            <Progress value={detectionProgress} />
          </div>
        )}

        {/* 检测结果 */}
        {detectedRegions.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">检测结果</h4>
              <Badge variant="secondary">
                {detectedRegions.length} 个区域
              </Badge>
            </div>

            {/* 最佳区域 */}
            {selectedRegion && (
              <Card className="p-3 border-green-200 bg-green-50">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-700">推荐区域</span>
                  <Badge variant="outline" className="text-green-600">
                    {(selectedRegion.confidence * 100).toFixed(1)}%
                  </Badge>
                </div>
                <div className="text-xs text-green-600 space-y-1">
                  <div>位置: ({selectedRegion.x}, {selectedRegion.y})</div>
                  <div>尺寸: {selectedRegion.width} × {selectedRegion.height}</div>
                  <div>帧索引: {selectedRegion.frameIndex}</div>
                </div>
              </Card>
            )}

            {/* 所有检测区域 */}
            {showAllRegions && (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {detectedRegions.map((region, index) => (
                  <Card 
                    key={index}
                    className={`p-2 cursor-pointer transition-colors ${
                      selectedRegion && 
                      region.x === selectedRegion.x && 
                      region.y === selectedRegion.y
                        ? 'border-green-200 bg-green-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => selectRegion(region)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">区域 {index + 1}</span>
                        <Badge variant="outline" className="text-xs">
                          {(region.confidence * 100).toFixed(1)}%
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500">
                        {region.width} × {region.height}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      位置: ({region.x}, {region.y}) | 帧: {region.frameIndex}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 检测画布 */}
        <div className="relative">
          <canvas
            ref={canvasRef}
            className="w-full border rounded"
            width={400}
            height={300}
          />
          {detectedRegions.length === 0 && !isDetecting && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 rounded">
              <div className="text-center">
                <Target className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">暂无检测结果</p>
              </div>
            </div>
          )}
        </div>

        {/* 使用说明 */}
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-xs text-blue-700">
              <p className="font-medium mb-1">检测说明</p>
              <ul className="space-y-1">
                <li>• 系统会从视频1/3处开始检测字幕区域</li>
                <li>• 绿色框表示推荐的字幕区域</li>
                <li>• 可以点击其他检测区域进行切换</li>
                <li>• 检测结果会用于后续的OCR识别</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
