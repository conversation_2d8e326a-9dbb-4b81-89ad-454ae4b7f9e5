import { NextApiRequest, NextApiResponse } from 'next'
import * as ort from 'onnxruntime-node'
import sharp from 'sharp'
import fs from 'fs'
import path from 'path'

const MODELS_DIR = path.join(process.cwd(), 'public', 'models')
const DET_MODEL_PATH = path.join(MODELS_DIR, 'ch_PP-OCRv5_mobile_det.onnx')

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { imagePath } = req.body

    if (!imagePath || !fs.existsSync(imagePath)) {
      return res.status(400).json({ message: '图片文件不存在' })
    }

    console.log('开始测试模型...')
    console.log('模型路径:', DET_MODEL_PATH)
    console.log('图片路径:', imagePath)

    // 检查模型文件是否存在
    if (!fs.existsSync(DET_MODEL_PATH)) {
      return res.status(400).json({ 
        success: false,
        message: '模型文件不存在',
        modelPath: DET_MODEL_PATH
      })
    }

    // 加载模型
    console.log('加载ONNX模型...')
    const session = await ort.InferenceSession.create(DET_MODEL_PATH)
    console.log('模型加载成功')

    // 获取模型输入信息
    const inputNames = session.inputNames
    const outputNames = session.outputNames
    console.log('输入名称:', inputNames)
    console.log('输出名称:', outputNames)

    // 获取输入形状信息
    const inputInfo = session.inputNames.map(name => ({
      name,
      shape: session.inputMetadata[name].dims
    }))
    console.log('输入信息:', inputInfo)

    // 预处理图片
    console.log('预处理图片...')
    const imageInfo = await sharp(imagePath).metadata()
    console.log('原始图片信息:', imageInfo)

    // PP-OCR模型通常需要32的倍数尺寸，尝试不同的预处理方式
    const testConfigs = [
      { width: 640, height: 640, method: 'fill' },
      { width: 960, height: 960, method: 'fill' },
      { width: 736, height: 736, method: 'fill' }, // 32的倍数
      { width: 640, height: 640, method: 'inside' }
    ]
    const results = []

    for (const config of testConfigs) {
      try {
        console.log(`测试配置: ${config.width}x${config.height} (${config.method})`)

        let imageBuffer
        if (config.method === 'fill') {
          imageBuffer = await sharp(imagePath)
            .resize(config.width, config.height, {
              fit: 'fill',
              background: { r: 0, g: 0, b: 0, alpha: 1 }
            })
            .raw()
            .toBuffer({ resolveWithObject: true })
        } else {
          imageBuffer = await sharp(imagePath)
            .resize(config.width, config.height, {
              fit: 'inside',
              withoutEnlargement: true,
              background: { r: 0, g: 0, b: 0, alpha: 1 }
            })
            .raw()
            .toBuffer({ resolveWithObject: true })
        }

        const { data, info } = imageBuffer
        const { width, height, channels } = info

        console.log(`预处理后: ${width}x${height}x${channels}`)

        // 转换为模型输入格式 - 简单的归一化
        const inputTensor = new Float32Array(1 * 3 * height * width)

        // 简单归一化到[0,1]
        for (let h = 0; h < height; h++) {
          for (let w = 0; w < width; w++) {
            const pixelIndex = h * width + w
            const dataIndex = pixelIndex * channels

            // RGB -> CHW 格式
            inputTensor[pixelIndex] = data[dataIndex] / 255.0
            inputTensor[height * width + pixelIndex] = data[dataIndex + 1] / 255.0
            inputTensor[height * width * 2 + pixelIndex] = data[dataIndex + 2] / 255.0
          }
        }

        // 推理
        const feeds: Record<string, ort.Tensor> = {}
        feeds[inputNames[0]] = new ort.Tensor('float32', inputTensor, [1, 3, height, width])

        console.log(`开始推理 (${config.width}x${config.height})...`)
        const output = await session.run(feeds)
        console.log(`推理成功 (${config.width}x${config.height})`)

        // 收集结果信息
        const outputInfo = outputNames.map(name => ({
          name,
          shape: output[name].dims,
          dataLength: output[name].data.length,
          dataType: output[name].type
        }))

        results.push({
          config,
          success: true,
          inputShape: [1, 3, height, width],
          outputInfo
        })

      } catch (error) {
        console.error(`配置 ${config.width}x${config.height} 推理失败:`, error)
        results.push({
          config,
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    res.status(200).json({
      success: true,
      modelPath: DET_MODEL_PATH,
      originalImageInfo: imageInfo,
      inputInfo,
      outputNames,
      testResults: results
    })

  } catch (error) {
    console.error('模型测试失败:', error)
    res.status(500).json({
      success: false,
      message: '模型测试失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}
