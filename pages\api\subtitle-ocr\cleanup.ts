import { NextApiRequest, NextApiResponse } from 'next'
import fs from 'fs'
import path from 'path'

interface CleanupRequest {
  framesDir?: string
  videoPath?: string
  subtitlePath?: string
  olderThanHours?: number // 清理多少小时前的文件，默认24小时
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { 
      framesDir, 
      videoPath, 
      subtitlePath, 
      olderThanHours = 24 
    }: CleanupRequest = req.body

    const cleanupResults = {
      framesDeleted: 0,
      videosDeleted: 0,
      subtitlesDeleted: 0,
      totalSpaceFreed: 0
    }

    // 清理指定的帧目录
    if (framesDir && fs.existsSync(framesDir)) {
      const spaceFreed = await cleanupDirectory(framesDir)
      cleanupResults.framesDeleted = 1
      cleanupResults.totalSpaceFreed += spaceFreed
    }

    // 清理指定的视频文件
    if (videoPath && fs.existsSync(videoPath)) {
      const stat = fs.statSync(videoPath)
      fs.unlinkSync(videoPath)
      cleanupResults.videosDeleted = 1
      cleanupResults.totalSpaceFreed += stat.size
    }

    // 清理指定的字幕文件
    if (subtitlePath && fs.existsSync(subtitlePath)) {
      const stat = fs.statSync(subtitlePath)
      fs.unlinkSync(subtitlePath)
      cleanupResults.subtitlesDeleted = 1
      cleanupResults.totalSpaceFreed += stat.size
    }

    // 清理过期的临时文件
    const tempCleanupResults = await cleanupOldTempFiles(olderThanHours)
    cleanupResults.framesDeleted += tempCleanupResults.framesDeleted
    cleanupResults.videosDeleted += tempCleanupResults.videosDeleted
    cleanupResults.subtitlesDeleted += tempCleanupResults.subtitlesDeleted
    cleanupResults.totalSpaceFreed += tempCleanupResults.totalSpaceFreed

    res.status(200).json({
      success: true,
      data: {
        ...cleanupResults,
        totalSpaceFreedMB: Math.round(cleanupResults.totalSpaceFreed / 1024 / 1024 * 100) / 100
      },
      message: `清理完成，释放空间 ${Math.round(cleanupResults.totalSpaceFreed / 1024 / 1024 * 100) / 100} MB`
    })

  } catch (error) {
    console.error('清理失败:', error)
    res.status(500).json({
      success: false,
      message: '清理失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 清理目录
async function cleanupDirectory(dirPath: string): Promise<number> {
  let totalSize = 0
  
  if (!fs.existsSync(dirPath)) {
    return 0
  }

  try {
    const files = fs.readdirSync(dirPath)
    
    for (const file of files) {
      const filePath = path.join(dirPath, file)
      const stat = fs.statSync(filePath)
      totalSize += stat.size
      
      if (stat.isDirectory()) {
        totalSize += await cleanupDirectory(filePath)
      } else {
        fs.unlinkSync(filePath)
      }
    }
    
    // 删除空目录
    fs.rmdirSync(dirPath)
  } catch (error) {
    console.warn(`清理目录失败: ${dirPath}`, error)
  }
  
  return totalSize
}

// 清理过期的临时文件
async function cleanupOldTempFiles(olderThanHours: number) {
  const results = {
    framesDeleted: 0,
    videosDeleted: 0,
    subtitlesDeleted: 0,
    totalSpaceFreed: 0
  }

  const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000)

  // 清理过期的帧目录
  const framesBaseDir = path.join(process.cwd(), 'data', 'frames')
  if (fs.existsSync(framesBaseDir)) {
    const frameDirs = fs.readdirSync(framesBaseDir)
    
    for (const dir of frameDirs) {
      const dirPath = path.join(framesBaseDir, dir)
      const stat = fs.statSync(dirPath)
      
      if (stat.isDirectory() && stat.mtime.getTime() < cutoffTime) {
        const spaceFreed = await cleanupDirectory(dirPath)
        results.framesDeleted++
        results.totalSpaceFreed += spaceFreed
      }
    }
  }

  // 清理过期的视频文件
  const videosDir = path.join(process.cwd(), 'data', 'videos')
  if (fs.existsSync(videosDir)) {
    const videoFiles = fs.readdirSync(videosDir)
    
    for (const file of videoFiles) {
      const filePath = path.join(videosDir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isFile() && stat.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filePath)
        results.videosDeleted++
        results.totalSpaceFreed += stat.size
      }
    }
  }

  // 清理过期的字幕文件
  const subtitlesDir = path.join(process.cwd(), 'data', 'subtitles')
  if (fs.existsSync(subtitlesDir)) {
    const subtitleFiles = fs.readdirSync(subtitlesDir)
    
    for (const file of subtitleFiles) {
      const filePath = path.join(subtitlesDir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isFile() && stat.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filePath)
        results.subtitlesDeleted++
        results.totalSpaceFreed += stat.size
      }
    }
  }

  return results
}
