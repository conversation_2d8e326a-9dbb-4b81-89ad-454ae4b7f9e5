'use client'

import React, { useCallback, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Upload, Video, FileVideo, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface VideoUploadAreaProps {
  onUpload: (file: File) => void
  isUploading?: boolean
  uploadProgress?: number
}

export function VideoUploadArea({ onUpload, isUploading = false, uploadProgress = 0 }: VideoUploadAreaProps) {
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string>('')

  // 验证文件
  const validateFile = useCallback((file: File): string | null => {
    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      return '请选择视频文件'
    }

    // 检查文件大小 (500MB限制)
    const maxSize = 500 * 1024 * 1024
    if (file.size > maxSize) {
      return '视频文件大小不能超过500MB'
    }

    // 检查文件扩展名
    const allowedExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    const fileName = file.name.toLowerCase()
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
    
    if (!hasValidExtension) {
      return `不支持的视频格式。支持的格式: ${allowedExtensions.join(', ')}`
    }

    return null
  }, [])

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    setError('')
    
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    onUpload(file)
  }, [validateFile, onUpload])

  // 处理拖拽
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (isUploading) return

    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect, isUploading])

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="h-full flex flex-col">
      {/* 上传区域 */}
      <Card 
        className={`flex-1 border-2 border-dashed transition-colors ${
          dragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-gray-400'
        } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <CardContent className="flex flex-col items-center justify-center h-full p-8 text-center">
          {isUploading ? (
            <>
              <Upload className="h-12 w-12 text-primary mb-4 animate-pulse" />
              <h3 className="text-lg font-semibold mb-2">正在上传视频...</h3>
              <p className="text-gray-500 mb-4">请稍候，正在处理您的视频文件</p>
              <div className="w-full max-w-xs">
                <Progress value={uploadProgress} className="mb-2" />
                <p className="text-sm text-gray-500">{uploadProgress}%</p>
              </div>
            </>
          ) : (
            <>
              <Video className="h-16 w-16 text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold mb-2">上传视频文件</h3>
              <p className="text-gray-500 mb-6">
                拖拽视频文件到此处，或点击选择文件
              </p>
              
              <Button 
                size="lg"
                onClick={() => {
                  const input = document.createElement('input')
                  input.type = 'file'
                  input.accept = 'video/*'
                  input.onchange = (e) => {
                    const file = (e.target as HTMLInputElement).files?.[0]
                    if (file) handleFileSelect(file)
                  }
                  input.click()
                }}
              >
                <FileVideo className="h-5 w-5 mr-2" />
                选择视频文件
              </Button>

              <div className="mt-6 text-sm text-gray-500">
                <p className="mb-1">支持的格式：MP4, AVI, MOV, MKV, WMV, FLV, WebM</p>
                <p>最大文件大小：500MB</p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 使用说明 */}
      <Card className="mt-4">
        <CardContent className="p-4">
          <h4 className="font-semibold mb-2">使用说明</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 上传包含硬字幕的视频文件</li>
            <li>• 系统将自动检测字幕区域</li>
            <li>• 使用OCR技术提取字幕文本</li>
            <li>• 支持导出SRT、VTT、TXT格式</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
