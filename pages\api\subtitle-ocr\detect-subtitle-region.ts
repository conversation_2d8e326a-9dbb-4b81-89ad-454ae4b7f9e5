import { NextApiRequest, NextApiResponse } from 'next'
import * as ort from 'onnxruntime-node'
import sharp from 'sharp'
import fs from 'fs'
import path from 'path'

interface SubtitleRegion {
  x: number
  y: number
  width: number
  height: number
  confidence: number
  frameIndex: number
  timestamp: number
}

interface DetectionRequest {
  framesDir: string
  videoPath: string
  startFromOneThird?: boolean // 是否从视频1/3处开始检测
  maxDetectionFrames?: number // 最大检测帧数，默认20
  confidenceThreshold?: number // 置信度阈值，默认0.5
}

const DET_MODEL_PATH = path.join(process.cwd(), 'public', 'models', 'ch_PP-OCRv5_mobile_det.onnx')
let detectionSession: ort.InferenceSession | null = null

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const {
      framesDir,
      videoPath,
      startFromOneThird = true,
      maxDetectionFrames = 20,
      confidenceThreshold = 0.5
    }: DetectionRequest = req.body

    if (!framesDir || !fs.existsSync(framesDir)) {
      return res.status(400).json({ message: '帧目录不存在' })
    }

    // 初始化检测模型
    await initializeDetectionModel()

    // 获取帧文件列表
    const frameFiles = fs.readdirSync(framesDir)
      .filter(file => file.endsWith('.jpg'))
      .sort()

    if (frameFiles.length === 0) {
      return res.status(400).json({ message: '未找到视频帧文件' })
    }

    // 选择要检测的帧（从1/3处开始或全部）
    const framesToDetect = selectFramesForDetection(frameFiles, startFromOneThird, maxDetectionFrames)

    // 检测字幕区域
    const detectedRegions: SubtitleRegion[] = []
    
    for (let i = 0; i < framesToDetect.length; i++) {
      const frameFile = framesToDetect[i]
      const framePath = path.join(framesDir, frameFile)
      
      try {
        const regions = await detectSubtitleInFrame(framePath, i, confidenceThreshold)
        detectedRegions.push(...regions)
      } catch (error) {
        console.warn(`帧 ${frameFile} 检测失败:`, error)
        continue
      }
    }

    // 分析检测结果，找出最可能的字幕区域
    const subtitleRegion = analyzeDetectedRegions(detectedRegions)

    res.status(200).json({
      success: true,
      data: {
        subtitleRegion,
        detectedRegions,
        totalFramesDetected: framesToDetect.length,
        totalRegionsFound: detectedRegions.length
      },
      message: subtitleRegion ? '成功检测到字幕区域' : '未检测到稳定的字幕区域'
    })

  } catch (error) {
    console.error('字幕区域检测失败:', error)
    res.status(500).json({
      success: false,
      message: '字幕区域检测失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 初始化检测模型
async function initializeDetectionModel() {
  if (!detectionSession) {
    try {
      console.log('加载文字检测模型...')
      detectionSession = await ort.InferenceSession.create(DET_MODEL_PATH)
    } catch (error) {
      console.error('检测模型加载失败:', error)
      throw new Error('检测模型加载失败')
    }
  }
}

// 选择要检测的帧
function selectFramesForDetection(frameFiles: string[], startFromOneThird: boolean, maxFrames: number): string[] {
  if (!startFromOneThird) {
    return frameFiles.slice(0, maxFrames)
  }

  // 从1/3处开始选择帧
  const startIndex = Math.floor(frameFiles.length / 3)
  const endIndex = Math.min(startIndex + maxFrames, frameFiles.length)
  
  return frameFiles.slice(startIndex, endIndex)
}

// 在单帧中检测字幕
async function detectSubtitleInFrame(framePath: string, frameIndex: number, threshold: number): Promise<SubtitleRegion[]> {
  if (!detectionSession) throw new Error('检测模型未加载')

  try {
    // 预处理图片 - 使用32的倍数尺寸避免形状问题
    const targetSize = 736 // 32的倍数
    const imageBuffer = await sharp(framePath)
      .resize(targetSize, targetSize, {
        fit: 'fill',
        background: { r: 0, g: 0, b: 0, alpha: 1 }
      })
      .raw()
      .toBuffer({ resolveWithObject: true })

    const { data, info } = imageBuffer
    const { width, height, channels } = info

    console.log(`帧 ${frameIndex} 预处理: ${width}x${height}x${channels}`)

    // 转换为模型输入格式 - 简单归一化
    const inputTensor = new Float32Array(1 * 3 * height * width)

    for (let h = 0; h < height; h++) {
      for (let w = 0; w < width; w++) {
        const pixelIndex = h * width + w
        const dataIndex = pixelIndex * channels

        // 简单归一化到[0,1]
        inputTensor[pixelIndex] = data[dataIndex] / 255.0
        inputTensor[height * width + pixelIndex] = data[dataIndex + 1] / 255.0
        inputTensor[height * width * 2 + pixelIndex] = data[dataIndex + 2] / 255.0
      }
    }

    // 推理 - 使用正确的输入名称
    const inputNames = detectionSession.inputNames
    const feeds: Record<string, ort.Tensor> = {}
    feeds[inputNames[0]] = new ort.Tensor('float32', inputTensor, [1, 3, height, width])

    const results = await detectionSession.run(feeds)
    console.log(`帧 ${frameIndex} 推理成功`)

    // 解析结果并筛选字幕区域
    return parseSubtitleRegions(results, threshold, frameIndex, width, height)

  } catch (error) {
    console.error(`帧 ${path.basename(framePath)} 检测失败:`, error)
    throw error
  }
}

// 解析字幕区域
function parseSubtitleRegions(results: any, threshold: number, frameIndex: number, width: number, height: number): SubtitleRegion[] {
  const regions: SubtitleRegion[] = []
  
  // 这里需要根据PP-OCR检测模型的具体输出格式来实现
  // 通常需要解析置信度图和几何信息
  
  // 简化实现：假设在图片下半部分检测到文字区域
  // 实际实现需要根据模型输出来解析
  
  // 模拟检测结果：在图片底部1/4区域
  const bottomRegion: SubtitleRegion = {
    x: width * 0.1,
    y: height * 0.75,
    width: width * 0.8,
    height: height * 0.2,
    confidence: 0.8,
    frameIndex,
    timestamp: frameIndex * 1.0 // 假设每秒1帧
  }
  
  regions.push(bottomRegion)
  
  return regions
}

// 分析检测结果，找出最稳定的字幕区域
function analyzeDetectedRegions(regions: SubtitleRegion[]): SubtitleRegion | null {
  if (regions.length === 0) return null

  // 按位置聚类，找出最常出现的字幕区域
  const clusters: SubtitleRegion[][] = []
  const clusterThreshold = 50 // 像素阈值

  for (const region of regions) {
    let addedToCluster = false

    for (const cluster of clusters) {
      const clusterCenter = cluster[0]
      const distance = Math.sqrt(
        Math.pow(region.x - clusterCenter.x, 2) + 
        Math.pow(region.y - clusterCenter.y, 2)
      )

      if (distance < clusterThreshold) {
        cluster.push(region)
        addedToCluster = true
        break
      }
    }

    if (!addedToCluster) {
      clusters.push([region])
    }
  }

  // 找出最大的聚类（最稳定的字幕区域）
  const largestCluster = clusters.reduce((max, cluster) => 
    cluster.length > max.length ? cluster : max, clusters[0]
  )

  if (!largestCluster || largestCluster.length === 0) return null

  // 计算聚类的平均位置和尺寸
  const avgX = largestCluster.reduce((sum, r) => sum + r.x, 0) / largestCluster.length
  const avgY = largestCluster.reduce((sum, r) => sum + r.y, 0) / largestCluster.length
  const avgWidth = largestCluster.reduce((sum, r) => sum + r.width, 0) / largestCluster.length
  const avgHeight = largestCluster.reduce((sum, r) => sum + r.height, 0) / largestCluster.length
  const avgConfidence = largestCluster.reduce((sum, r) => sum + r.confidence, 0) / largestCluster.length

  return {
    x: Math.round(avgX),
    y: Math.round(avgY),
    width: Math.round(avgWidth),
    height: Math.round(avgHeight),
    confidence: avgConfidence,
    frameIndex: largestCluster[0].frameIndex,
    timestamp: largestCluster[0].timestamp
  }
}
