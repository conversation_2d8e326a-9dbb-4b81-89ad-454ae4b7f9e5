import { NextApiRequest, NextApiResponse } from 'next'
import fs from 'fs'
import path from 'path'

interface SubtitleEntry {
  index: number
  startTime: number
  endTime: number
  text: string
  confidence: number
}

interface GenerateSubtitleRequest {
  framesDir: string
  subtitleRegion: {
    x: number
    y: number
    width: number
    height: number
  }
  ocrResults: Array<{
    frameIndex: number
    timestamp: number
    text: string
    confidence: number
  }>
  format?: 'srt' | 'vtt' | 'txt'
  minDuration?: number // 最小字幕持续时间（秒），默认1秒
  mergeSimilar?: boolean // 是否合并相似字幕，默认true
  similarityThreshold?: number // 相似度阈值，默认0.8
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const {
      framesDir,
      subtitleRegion,
      ocrResults,
      format = 'srt',
      minDuration = 1.0,
      mergeSimilar = true,
      similarityThreshold = 0.8
    }: GenerateSubtitleRequest = req.body

    if (!ocrResults || ocrResults.length === 0) {
      return res.status(400).json({ message: 'OCR结果为空' })
    }

    // 处理OCR结果，生成字幕条目
    let subtitleEntries = processOCRResults(ocrResults, minDuration)

    // 去重和合并相似字幕
    if (mergeSimilar) {
      subtitleEntries = mergeSimilarSubtitles(subtitleEntries, similarityThreshold)
    }

    // 生成字幕文件内容
    const subtitleContent = generateSubtitleContent(subtitleEntries, format)

    // 保存字幕文件
    const outputDir = path.join(process.cwd(), 'data', 'subtitles')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    const timestamp = Date.now()
    const filename = `subtitle_${timestamp}.${format}`
    const filePath = path.join(outputDir, filename)
    
    fs.writeFileSync(filePath, subtitleContent, 'utf-8')

    res.status(200).json({
      success: true,
      data: {
        filename,
        filePath,
        subtitleEntries,
        totalEntries: subtitleEntries.length,
        format,
        content: subtitleContent
      },
      message: `成功生成 ${subtitleEntries.length} 条字幕`
    })

  } catch (error) {
    console.error('字幕生成失败:', error)
    res.status(500).json({
      success: false,
      message: '字幕生成失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 处理OCR结果，生成字幕条目
function processOCRResults(ocrResults: any[], minDuration: number): SubtitleEntry[] {
  const entries: SubtitleEntry[] = []
  
  // 按时间戳排序
  const sortedResults = ocrResults.sort((a, b) => a.timestamp - b.timestamp)
  
  for (let i = 0; i < sortedResults.length; i++) {
    const current = sortedResults[i]
    const next = sortedResults[i + 1]
    
    // 计算结束时间
    let endTime: number
    if (next) {
      endTime = Math.max(current.timestamp + minDuration, next.timestamp - 0.1)
    } else {
      endTime = current.timestamp + minDuration
    }
    
    entries.push({
      index: i + 1,
      startTime: current.timestamp,
      endTime,
      text: current.text.trim(),
      confidence: current.confidence
    })
  }
  
  return entries
}

// 合并相似字幕
function mergeSimilarSubtitles(entries: SubtitleEntry[], threshold: number): SubtitleEntry[] {
  if (entries.length <= 1) return entries
  
  const merged: SubtitleEntry[] = []
  let currentEntry = { ...entries[0] }
  
  for (let i = 1; i < entries.length; i++) {
    const nextEntry = entries[i]
    const similarity = calculateTextSimilarity(currentEntry.text, nextEntry.text)
    
    if (similarity >= threshold) {
      // 合并相似字幕
      currentEntry.endTime = nextEntry.endTime
      currentEntry.confidence = Math.max(currentEntry.confidence, nextEntry.confidence)
      
      // 如果下一个字幕的文本更长或置信度更高，使用它的文本
      if (nextEntry.text.length > currentEntry.text.length || 
          nextEntry.confidence > currentEntry.confidence) {
        currentEntry.text = nextEntry.text
      }
    } else {
      // 不相似，保存当前字幕并开始新的
      merged.push(currentEntry)
      currentEntry = { ...nextEntry, index: merged.length + 1 }
    }
  }
  
  // 添加最后一个字幕
  merged.push(currentEntry)
  
  // 重新编号
  merged.forEach((entry, index) => {
    entry.index = index + 1
  })
  
  return merged
}

// 计算文本相似度
function calculateTextSimilarity(text1: string, text2: string): number {
  if (text1 === text2) return 1.0
  if (!text1 || !text2) return 0.0
  
  // 简单的编辑距离相似度计算
  const maxLength = Math.max(text1.length, text2.length)
  const distance = levenshteinDistance(text1, text2)
  
  return 1 - (distance / maxLength)
}

// 计算编辑距离
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      )
    }
  }
  
  return matrix[str2.length][str1.length]
}

// 生成字幕文件内容
function generateSubtitleContent(entries: SubtitleEntry[], format: string): string {
  switch (format) {
    case 'srt':
      return generateSRT(entries)
    case 'vtt':
      return generateVTT(entries)
    case 'txt':
      return generateTXT(entries)
    default:
      throw new Error(`不支持的字幕格式: ${format}`)
  }
}

// 生成SRT格式
function generateSRT(entries: SubtitleEntry[]): string {
  return entries.map(entry => {
    const startTime = formatSRTTime(entry.startTime)
    const endTime = formatSRTTime(entry.endTime)
    
    return `${entry.index}\n${startTime} --> ${endTime}\n${entry.text}\n`
  }).join('\n')
}

// 生成VTT格式
function generateVTT(entries: SubtitleEntry[]): string {
  const header = 'WEBVTT\n\n'
  const content = entries.map(entry => {
    const startTime = formatVTTTime(entry.startTime)
    const endTime = formatVTTTime(entry.endTime)
    
    return `${startTime} --> ${endTime}\n${entry.text}\n`
  }).join('\n')
  
  return header + content
}

// 生成TXT格式
function generateTXT(entries: SubtitleEntry[]): string {
  return entries.map(entry => entry.text).join('\n')
}

// 格式化SRT时间
function formatSRTTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

// 格式化VTT时间
function formatVTTTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`
}
