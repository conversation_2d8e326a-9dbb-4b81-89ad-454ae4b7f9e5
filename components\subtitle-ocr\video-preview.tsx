'use client'

import React, { useRef, useEffect, useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import { Play, Pause, Ski<PERSON><PERSON><PERSON>, Ski<PERSON>Forward, Volume2, Maximize } from 'lucide-react'

interface VideoInfo {
  filename: string
  size: number
  duration: number
  width: number
  height: number
  fps: number
  format: string
  uploadPath: string
}

interface DetectedRegion {
  x: number
  y: number
  width: number
  height: number
  confidence: number
}

interface OCRConfig {
  subtitleRegion: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

interface VideoPreviewProps {
  videoInfo: VideoInfo
  detectedRegion?: DetectedRegion | null
  ocrConfig: OCRConfig
}

export function VideoPreview({ videoInfo, detectedRegion, ocrConfig }: VideoPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [showRegionOverlay, setShowRegionOverlay] = useState(true)

  // 格式化时间
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 播放/暂停
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 跳转到指定时间
  const seekTo = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      setCurrentTime(time)
    }
  }

  // 快进/快退
  const skip = (seconds: number) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
      seekTo(newTime)
    }
  }

  // 设置音量
  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0]
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  // 绘制字幕区域覆盖层
  const drawRegionOverlay = () => {
    const canvas = canvasRef.current
    const video = videoRef.current
    
    if (!canvas || !video || !showRegionOverlay) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸匹配视频显示尺寸
    const rect = video.getBoundingClientRect()
    canvas.width = rect.width
    canvas.height = rect.height

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // 绘制配置的字幕区域
    const configRegion = {
      x: (ocrConfig.subtitleRegion.left / 100) * canvas.width,
      y: (ocrConfig.subtitleRegion.top / 100) * canvas.height,
      width: ((ocrConfig.subtitleRegion.right - ocrConfig.subtitleRegion.left) / 100) * canvas.width,
      height: ((ocrConfig.subtitleRegion.bottom - ocrConfig.subtitleRegion.top) / 100) * canvas.height
    }

    // 绘制配置区域（蓝色虚线框）
    ctx.strokeStyle = '#3b82f6'
    ctx.lineWidth = 2
    ctx.setLineDash([5, 5])
    ctx.strokeRect(configRegion.x, configRegion.y, configRegion.width, configRegion.height)

    // 添加标签
    ctx.fillStyle = '#3b82f6'
    ctx.font = '12px sans-serif'
    ctx.fillText('配置区域', configRegion.x, configRegion.y - 5)

    // 绘制检测到的字幕区域（如果有）
    if (detectedRegion) {
      const detectedRect = {
        x: (detectedRegion.x / videoInfo.width) * canvas.width,
        y: (detectedRegion.y / videoInfo.height) * canvas.height,
        width: (detectedRegion.width / videoInfo.width) * canvas.width,
        height: (detectedRegion.height / videoInfo.height) * canvas.height
      }

      // 绘制检测区域（绿色实线框）
      ctx.strokeStyle = '#10b981'
      ctx.lineWidth = 2
      ctx.setLineDash([])
      ctx.strokeRect(detectedRect.x, detectedRect.y, detectedRect.width, detectedRect.height)

      // 添加标签
      ctx.fillStyle = '#10b981'
      ctx.fillText(`检测区域 (${(detectedRegion.confidence * 100).toFixed(1)}%)`, detectedRect.x, detectedRect.y - 5)
    }
  }

  // 视频事件处理
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
    }
  }, [])

  // 重绘覆盖层
  useEffect(() => {
    drawRegionOverlay()
  }, [ocrConfig, detectedRegion, showRegionOverlay])

  // 窗口大小变化时重绘
  useEffect(() => {
    const handleResize = () => {
      setTimeout(drawRegionOverlay, 100)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="h-full flex flex-col">
      {/* 视频信息 */}
      <div className="flex-shrink-0 mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">文件名：</span>
            <span className="text-gray-600 truncate block">{videoInfo.filename}</span>
          </div>
          <div>
            <span className="font-medium">时长：</span>
            <span className="text-gray-600">{formatTime(videoInfo.duration)}</span>
          </div>
          <div>
            <span className="font-medium">分辨率：</span>
            <span className="text-gray-600">{videoInfo.width} × {videoInfo.height}</span>
          </div>
          <div>
            <span className="font-medium">帧率：</span>
            <span className="text-gray-600">{videoInfo.fps.toFixed(1)} fps</span>
          </div>
        </div>
      </div>

      {/* 视频播放器 */}
      <div className="flex-1 relative bg-black rounded-lg overflow-hidden min-h-0">
        <video
          ref={videoRef}
          className="w-full h-full object-contain"
          src={`/api/video-stream?path=${encodeURIComponent(videoInfo.uploadPath)}`}
          onLoadedData={drawRegionOverlay}
        />

        {/* 字幕区域覆盖层 */}
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 w-full h-full pointer-events-none"
        />
      </div>

      {/* 控制栏 */}
      <div className="flex-shrink-0 mt-4 space-y-3">
        {/* 进度条 */}
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-500 w-12 text-center">{formatTime(currentTime)}</span>
          <Slider
            value={[currentTime]}
            max={duration}
            step={0.1}
            onValueChange={(value) => seekTo(value[0])}
            className="flex-1"
          />
          <span className="text-sm text-gray-500 w-12 text-center">{formatTime(duration)}</span>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => skip(-10)}>
              <SkipBack className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={togglePlay}>
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={() => skip(10)}>
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={showRegionOverlay ? "default" : "outline"}
              size="sm"
              onClick={() => setShowRegionOverlay(!showRegionOverlay)}
            >
              区域显示
            </Button>

            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              <Slider
                value={[volume]}
                max={1}
                step={0.1}
                onValueChange={handleVolumeChange}
                className="w-20"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
