'use client'

import React, { useState, useRef, useCallback } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, Play, Eye, AlertCircle } from 'lucide-react'
import { VideoUploadArea } from '@/components/subtitle-ocr/video-upload-area'
import { VideoPreview } from '@/components/subtitle-ocr/video-preview'
import { OCRSettings } from '@/components/subtitle-ocr/ocr-settings'
import { ProcessingProgress } from '@/components/subtitle-ocr/processing-progress'
import { SubtitlePreview } from '@/components/subtitle-ocr/subtitle-preview'

interface VideoInfo {
  filename: string
  size: number
  duration: number
  width: number
  height: number
  fps: number
  format: string
  uploadPath: string
}

interface OCRConfig {
  language: string
  detectThreshold: number
  recognizeThreshold: number
  frameRate: number
  subtitleRegion: {
    top: number
    bottom: number
    left: number
    right: number
  }
}

interface ProcessingState {
  stage: 'idle' | 'uploading' | 'extracting' | 'detecting' | 'recognizing' | 'generating' | 'completed'
  progress: number
  message: string
  error?: string
}

interface SubtitleEntry {
  index: number
  startTime: number
  endTime: number
  text: string
  confidence: number
}

export function SubtitleOCRExtractor() {
  const [videoInfo, setVideoInfo] = useState<VideoInfo | null>(null)
  const [ocrConfig, setOCRConfig] = useState<OCRConfig>({
    language: 'chinese',
    detectThreshold: 0.3,
    recognizeThreshold: 0.5,
    frameRate: 1,
    subtitleRegion: {
      top: 75,
      bottom: 95,
      left: 10,
      right: 90
    }
  })
  const [processingState, setProcessingState] = useState<ProcessingState>({
    stage: 'idle',
    progress: 0,
    message: ''
  })
  const [subtitleEntries, setSubtitleEntries] = useState<SubtitleEntry[]>([])
  const [detectedRegion, setDetectedRegion] = useState<any>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理视频上传
  const handleVideoUpload = useCallback(async (file: File) => {
    setProcessingState({
      stage: 'uploading',
      progress: 0,
      message: '正在上传视频文件...'
    })

    try {
      const formData = new FormData()
      formData.append('video', file)

      const response = await fetch('/api/subtitle-ocr/simple-upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message)
      }

      setVideoInfo(result.data)
      setProcessingState({
        stage: 'idle',
        progress: 100,
        message: '视频上传成功'
      })

    } catch (error) {
      setProcessingState({
        stage: 'idle',
        progress: 0,
        message: '',
        error: error instanceof Error ? error.message : '上传失败'
      })
    }
  }, [])

  // 开始字幕提取
  const handleStartExtraction = useCallback(async () => {
    if (!videoInfo) return

    try {
      // 1. 提取视频帧
      setProcessingState({
        stage: 'extracting',
        progress: 10,
        message: '正在提取视频帧...'
      })

      const framesResponse = await fetch('/api/subtitle-ocr/extract-frames', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          videoPath: videoInfo.uploadPath,
          frameRate: ocrConfig.frameRate
        })
      })

      const framesResult = await framesResponse.json()
      if (!framesResult.success) throw new Error(framesResult.message)

      // 2. 检测字幕区域
      setProcessingState({
        stage: 'detecting',
        progress: 30,
        message: '正在检测字幕区域...'
      })

      const detectionResponse = await fetch('/api/subtitle-ocr/detect-subtitle-region', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          framesDir: framesResult.data.framesDir,
          videoPath: videoInfo.uploadPath,
          confidenceThreshold: ocrConfig.detectThreshold
        })
      })

      const detectionResult = await detectionResponse.json()
      if (!detectionResult.success) throw new Error(detectionResult.message)

      setDetectedRegion(detectionResult.data.subtitleRegion)

      // 3. OCR识别
      setProcessingState({
        stage: 'recognizing',
        progress: 60,
        message: '正在进行OCR识别...'
      })

      // 批量处理所有帧的OCR
      const ocrResults: Array<{
        frameIndex: number
        timestamp: number
        text: string
        confidence: number
      }> = []
      const frames = framesResult.data.frames

      for (let i = 0; i < frames.length; i++) {
        const frame = frames[i]

        try {
          setProcessingState({
            stage: 'recognizing',
            progress: 60 + (i / frames.length) * 20,
            message: `正在识别第 ${i + 1}/${frames.length} 帧...`
          })

          const ocrResponse = await fetch('/api/subtitle-ocr/ocr-inference', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              imagePath: frame.path,
              detectThreshold: ocrConfig.detectThreshold,
              recognizeThreshold: ocrConfig.recognizeThreshold
            })
          })

          const ocrResult = await ocrResponse.json()

          if (ocrResult.success && ocrResult.data.results.length > 0) {
            // 将OCR结果转换为字幕格式
            ocrResult.data.results.forEach((result: any) => {
              ocrResults.push({
                frameIndex: i,
                timestamp: frame.timestamp,
                text: result.text,
                confidence: result.confidence
              })
            })
          }
        } catch (error) {
          console.warn(`帧 ${frame.filename} OCR识别失败:`, error)
          continue
        }
      }

      // 4. 生成字幕
      setProcessingState({
        stage: 'generating',
        progress: 80,
        message: '正在生成字幕文件...'
      })

      const subtitleResponse = await fetch('/api/subtitle-ocr/generate-subtitle', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          framesDir: framesResult.data.framesDir,
          subtitleRegion: detectionResult.data.subtitleRegion,
          ocrResults: ocrResults,
          format: 'srt'
        })
      })

      const subtitleResult = await subtitleResponse.json()
      if (!subtitleResult.success) throw new Error(subtitleResult.message)

      setSubtitleEntries(subtitleResult.data.subtitleEntries)
      setProcessingState({
        stage: 'completed',
        progress: 100,
        message: `成功提取 ${subtitleResult.data.totalEntries} 条字幕`
      })

    } catch (error) {
      setProcessingState({
        stage: 'idle',
        progress: 0,
        message: '',
        error: error instanceof Error ? error.message : '提取失败'
      })
    }
  }, [videoInfo, ocrConfig])

  // 导出字幕
  const handleExportSubtitle = useCallback((format: 'srt' | 'vtt' | 'txt') => {
    // 实现导出逻辑
    console.log('导出字幕:', format)
  }, [subtitleEntries])

  return (
    <div className="h-full flex flex-col">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-2xl font-bold">硬字幕提取</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={processingState.stage !== 'idle'}
          >
            <Upload className="h-4 w-4 mr-2" />
            上传视频
          </Button>
          <Button
            size="sm"
            onClick={handleStartExtraction}
            disabled={!videoInfo || processingState.stage !== 'idle'}
          >
            <Play className="h-4 w-4 mr-2" />
            开始提取
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：视频预览区域 (2/3) */}
        <div className="flex-1 p-4 overflow-hidden">
          <Card className="h-full flex flex-col">
            <CardHeader className="flex-shrink-0">
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                视频预览
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-hidden">
              {videoInfo ? (
                <VideoPreview
                  videoInfo={videoInfo}
                  detectedRegion={detectedRegion}
                  ocrConfig={ocrConfig}
                />
              ) : (
                <VideoUploadArea
                  onUpload={handleVideoUpload}
                  isUploading={processingState.stage === 'uploading'}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* 右侧：参数设置和结果 (1/3) */}
        <div className="w-1/3 p-4 pl-0 flex flex-col overflow-hidden">
          <Card className="flex-1 flex flex-col overflow-hidden">
            <Tabs defaultValue="settings" className="flex-1 flex flex-col overflow-hidden">
              <TabsList className="grid w-full grid-cols-2 m-4 mb-0 flex-shrink-0">
                <TabsTrigger value="settings">参数设置</TabsTrigger>
                <TabsTrigger value="results">提取结果</TabsTrigger>
              </TabsList>

              <TabsContent value="settings" className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto px-4 pb-4">
                  <OCRSettings
                    config={ocrConfig}
                    onChange={setOCRConfig}
                    disabled={processingState.stage !== 'idle'}
                  />
                </div>
              </TabsContent>

              <TabsContent value="results" className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto px-4 pb-4">
                  <SubtitlePreview
                    entries={subtitleEntries}
                    onExport={handleExportSubtitle}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </Card>
        </div>
      </div>

      {/* 底部：处理进度 */}
      {processingState.stage !== 'idle' && (
        <div className="p-4 border-t">
          <ProcessingProgress 
            state={processingState}
          />
        </div>
      )}

      {/* 错误提示 */}
      {processingState.error && (
        <Alert className="m-4 mt-0" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{processingState.error}</AlertDescription>
        </Alert>
      )}

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file) handleVideoUpload(file)
        }}
      />
    </div>
  )
}
