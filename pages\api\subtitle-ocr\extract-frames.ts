import { NextApiRequest, NextApiResponse } from 'next'
import ffmpeg from 'fluent-ffmpeg'
import fs from 'fs'
import path from 'path'
import sharp from 'sharp'

interface ExtractFramesRequest {
  videoPath: string
  startTime?: number // 开始时间（秒），默认为视频1/3处
  endTime?: number   // 结束时间（秒），默认为视频结束
  frameRate?: number // 提取帧率（每秒提取几帧），默认1
  maxFrames?: number // 最大帧数，默认100
}

interface FrameInfo {
  timestamp: number
  filename: string
  path: string
  width: number
  height: number
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { 
      videoPath, 
      startTime, 
      endTime, 
      frameRate = 1, 
      maxFrames = 100 
    }: ExtractFramesRequest = req.body

    if (!videoPath || !fs.existsSync(videoPath)) {
      return res.status(400).json({ message: '视频文件不存在' })
    }

    // 获取视频信息
    const videoInfo = await getVideoMetadata(videoPath)
    const videoDuration = videoInfo.duration

    // 计算开始和结束时间
    const actualStartTime = startTime ?? Math.floor(videoDuration / 3) // 默认从1/3处开始
    const actualEndTime = endTime ?? videoDuration

    if (actualStartTime >= actualEndTime) {
      return res.status(400).json({ message: '开始时间必须小于结束时间' })
    }

    // 创建帧输出目录
    const framesDir = path.join(process.cwd(), 'data', 'frames', `${Date.now()}`)
    if (!fs.existsSync(framesDir)) {
      fs.mkdirSync(framesDir, { recursive: true })
    }

    // 提取帧
    const frames = await extractVideoFrames({
      videoPath,
      outputDir: framesDir,
      startTime: actualStartTime,
      endTime: actualEndTime,
      frameRate,
      maxFrames
    })

    res.status(200).json({
      success: true,
      data: {
        frames,
        totalFrames: frames.length,
        videoDuration,
        extractedDuration: actualEndTime - actualStartTime,
        framesDir
      },
      message: `成功提取 ${frames.length} 帧`
    })

  } catch (error) {
    console.error('帧提取失败:', error)
    res.status(500).json({
      success: false,
      message: '帧提取失败',
      error: error instanceof Error ? error.message : '未知错误'
    })
  }
}

// 获取视频元数据
function getVideoMetadata(videoPath: string): Promise<{ duration: number; width: number; height: number }> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err, metadata) => {
      if (err) {
        reject(err)
        return
      }

      const videoStream = metadata.streams.find(stream => stream.codec_type === 'video')
      if (!videoStream) {
        reject(new Error('未找到视频流'))
        return
      }

      resolve({
        duration: metadata.format.duration || 0,
        width: videoStream.width || 0,
        height: videoStream.height || 0
      })
    })
  })
}

// 提取视频帧
function extractVideoFrames(options: {
  videoPath: string
  outputDir: string
  startTime: number
  endTime: number
  frameRate: number
  maxFrames: number
}): Promise<FrameInfo[]> {
  return new Promise((resolve, reject) => {
    const { videoPath, outputDir, startTime, endTime, frameRate, maxFrames } = options
    const frames: FrameInfo[] = []
    
    // 计算提取间隔
    const duration = endTime - startTime
    const totalPossibleFrames = Math.floor(duration * frameRate)
    const actualFrameCount = Math.min(totalPossibleFrames, maxFrames)
    const interval = duration / actualFrameCount

    let frameIndex = 0
    const extractFrame = async (timestamp: number) => {
      const filename = `frame_${frameIndex.toString().padStart(6, '0')}.jpg`
      const outputPath = path.join(outputDir, filename)

      return new Promise<FrameInfo>((resolveFrame, rejectFrame) => {
        ffmpeg(videoPath)
          .seekInput(timestamp)
          .frames(1)
          .output(outputPath)
          .on('end', async () => {
            try {
              // 获取图片尺寸
              const metadata = await sharp(outputPath).metadata()
              resolveFrame({
                timestamp,
                filename,
                path: outputPath,
                width: metadata.width || 0,
                height: metadata.height || 0
              })
            } catch (error) {
              rejectFrame(error)
            }
          })
          .on('error', rejectFrame)
          .run()
      })
    }

    // 批量提取帧
    const extractPromises: Promise<FrameInfo>[] = []
    for (let i = 0; i < actualFrameCount; i++) {
      const timestamp = startTime + (i * interval)
      frameIndex = i
      extractPromises.push(extractFrame(timestamp))
    }

    Promise.all(extractPromises)
      .then(resolve)
      .catch(reject)
  })
}
